# Set to true to add reviewers to pull requests
addReviewers: false

# Set to true to add assignees to pull requests
addAssignees: true

# A list of reviewers to be added to pull requests (GitHub user name)
reviewers:
  - spencer<PERSON>on
  - maths22
  - slaughter550
  - dustin-cowles

# A list of keywords to be skipped if pull requests include it
skipKeywords: []

# A number of reviewers added to the pull request
# Set 0 to add all the reviewers
numberOfReviewers: 1
