{
  "version": "1.0.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Jest: current file",
      //"env": { "NODE_ENV": "test" },
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["${fileBasenameNoExtension}", "--config", "jest.config.js"],
      "console": "integratedTerminal",
      "disableOptimisticBPs": true,
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest"
      }
    },
    {
      "type": "rdbg",
      "request": "launch",
      "name": "Launch Rails Server",
      "command": "bin/rails",
      "script": "server"
    },
    {
      "type": "rdbg",
      "name": "Attach to web server",
      "request": "attach",
    },
    {
      "type": "rdbg",
      "name": "Attach with rdbg (tcp 12345)",
      "request": "attach",
      "debugPort": "localhost:12345",
      "localfsMap": "/usr/src/app:${workspaceFolder}"
    },
    {
      "type": "rdbg",
      "request": "launch",
      "name": "Run Current Spec File",
      "rdbgPath": "bin/rdbg-spring",
      "command": "rspec",
      "script": "${file}"
    },
    {
      "type": "rdbg",
      "request": "launch",
      "name": "Run Current Spec File and Line",
      "rdbgPath": "bin/rdbg-spring",
      "command": "rspec",
      "script": "${file}:${lineNumber}"
    }
  ]
}
