<%
# Copyright (C) 2012 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<% js_bundle :settings_sidebar
   css_bundle :settings_sidebar
%>
<% blueprint_subscription ||= nil %>
<% show_publish_controls ||= false %>
<% show_youtube_migration ||= @context&.root_account&.feature_enabled?(:youtube_migration) || false %>
<% if blueprint_subscription %>
  <script>
    function gotoBlueprintInfo() {
      let path = window.location.pathname;
      // toggle the trailing / so the browser actually navigates
      if (path.lastIndexOf('/') === path.length - 1) {
        path = path.substr(0, path.length-1);
      } else {
        path = path + '/';
      }
      const shabang = '<%= "#!/blueprint/blueprint_subscriptions/#{blueprint_subscription.id}/#{blueprint_subscription.last_migration_id}" %>';
      window.location.replace(path + shabang);
    }
  </script>
<% end %>
<div>
  <% if show_publish_controls %>
    <div id="course_status">
      <h4>
        <%= t('headers.course_status', %{Course Status}) %>
      </h4>
      <div class="course_publish_button" id="course_publish_button"></div>
    </div>
  <% end %>

  <%= external_tools_menu_items(@course_settings_sub_navigation_tools, {link_class: "Button Button--link Button--link--has-divider Button--course-settings course-settings-sub-navigation-lti", settings_key: :course_settings_sub_navigation, remove_space_between_icon_and_text: true}) %>
  <% if can_do(@context, @current_user, :read_reports) %>
    <%= link_to context_url(@context, :context_statistics_url), :class => ' Button Button--link Button--link--has-divider Button--course-settings' do %>
      <i class="icon-stats"></i><%= t('links.stats', 'Course Statistics') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :read) %>
    <%= link_to calendar_url_for(@context), :class => 'Button Button--link Button--link--has-divider Button--course-settings', :id => 'course_calendar_link' do %>
      <i class="icon-calendar-day"></i><%= t('links.calendar', 'Course Calendar') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :manage_courses_conclude) %>
    <% if @context.completed? %>
      <%= form_tag context_url(@context, :context_unconclude_url) do %>
        <button class="Button Button--link Button--link--has-divider Button--course-settings" type="submit">
          <i class="icon-add"></i><%= t('buttons.unconclude', 'Un-Conclude Course') %>
        </button>
      <% end %>
    <% elsif !@context.soft_concluded? %>
      <%= link_to context_url(@context, :context_confirm_action_url, :event => 'conclude'), :class => 'Button Button--link Button--link--has-divider Button--course-settings' do %>
        <i class="icon-lock"></i><%= t('links.conclude', 'Conclude this Course') %>
      <% end %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :delete) && !@context.deleted? %>
    <%= link_to context_url(@context, :context_confirm_action_url, :event => 'delete'), :class => 'Button Button--link Button--link--has-divider Button--course-settings' do %>
      <i class="icon-trash"></i><%= t('links.delete', 'Delete this Course') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :read) && show_user_create_course_button(@current_user, @context.account) %>
    <%= link_to context_url(@context, :context_start_copy_url), :class => 'Button Button--link Button--link--has-divider Button--course-settings copy_course_link' do %>
      <i class="icon-copy-course"></i><%= t('links.copy', 'Copy this Course') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :manage_course_content_add) %>
    <%= link_to context_url(@context, :context_content_migrations_url), :class => 'Button Button--link Button--link--has-divider Button--course-settings import_content' do %>
      <i class="icon-upload"></i><%= t('links.import', 'Import Course Content') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :read) %>
    <%= link_to context_url(@context, :course_content_exports_url), :class => 'Button Button--link Button--link--has-divider Button--course-settings' do %>
      <i class="icon-download"></i><%= t('links.export_content', 'Export Course Content') %>
    <% end %>
  <% end %>

  <% if can_do(@context, @current_user, :reset_content) && !MasterCourses::MasterTemplate.is_master_course?(@context) %>
    <%= link_to context_url(@context, :course_reset_url), :class => ' Button Button--link Button--link--has-divider Button--course-settings reset_course_content_button' do %>
      <i class="icon-reset"></i><%= t('links.reset', 'Reset Course Content') %>
    <% end %>

    <div <%= hidden(include_style: true) %> id="reset_course_content_dialog">
      <p><%= mt('help.reset_course_content', 'Resetting course content will permanently delete all associated assignments, discussions, quizzes, modules, rubrics, pages, files, learning outcomes, question banks, collaborations, conferences, or any other content. This action is irreversible, and the data *cannot* be recovered. Are you sure you wish to continue?') %></p>

      <%= form_for @context, :url => context_url(@context, :course_reset_url), :html => { :method => :post } do %>
        <div class="button-container">
          <button type="button" class="btn cancel_button">
            <%= t('#buttons.cancel', 'Cancel') %>
          </button>
          <button type="submit" class="btn btn-danger submit_button">
            <%= t('buttons.reset', 'Reset Course Content') %>
          </button>
        </div>
      <% end %>
    </div>
  <% end %>

  <% if can_do(@context, @current_user, *RoleOverride::GRANULAR_MANAGE_COURSE_CONTENT_PERMISSIONS) %>
    <%= link_to context_url(@context, :course_link_validator_url), :class => 'Button Button--link Button--link--has-divider Button--course-settings validator_link' do %>
      <i class="icon-link"></i><%= t('Validate Links in Content') %>
    <% end %>

    <% if show_youtube_migration %>
      <%= link_to context_url(@context, :course_youtube_migration_url), :class => 'Button Button--link Button--link--has-divider Button--course-settings youtube_migration' do %>
        <i class="icon-media"></i><%= t('Replace YouTube Embed') %>
      <% end %>
    <% end %>

    <% if blueprint_subscription %>
      <%= link_to "#", :onclick => 'gotoBlueprintInfo()', :class => 'Button Button--link Button--link--has-divider Button--course-settings blueprint_information_button' do %>
        <i class="icon-blueprint"></i><%= t('Blueprint Information') %>
      <% end %>
    <% end %>
  <% end %>

  <table class="summary">
    <caption>
      <h3><%= t('headings.current_users', 'Current Users') %></h3>
    </caption>

    <tbody>
      <% @all_roles.each do |base_role| %>
        <tr>
          <th><%= base_role[:plural_label] %>:</th>
          <td><%= n(user_count(base_role[:count])) %></td>
        </tr>
        <% base_role[:custom_roles].each do |cr|
           next if skip_custom_role?(cr)
        %>
          <tr>
            <th>
              <%= cr[:label] %><% if cr[:workflow_state] == 'inactive' %> (<%= t('role.inactive', 'inactive') %>)<% end %>:
            </th>
            <td><%= n(user_count(cr[:count])) %></td>
          </tr>
        <% end %>
      <% end %>
    </tbody>
  </table>
</div>
