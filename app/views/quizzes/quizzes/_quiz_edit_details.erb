<%
# Copyright (C) 2016 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<%
content_is_locked = !!js_env.dig(:MASTER_COURSE_DATA, "is_master_course_child_content") &&
                    !!js_env.dig(:MASTER_COURSE_DATA, "restricted_by_master_course") &&
                      js_env.dig(:MASTER_COURSE_DATA, "master_course_restrictions", :content)
%>
<div id="options_tab">
  <%= form_tag context_url(@context, :context_quiz_url, @quiz.id), {:method => :put, :id => "quiz_options_form", :class => 'form-horizontal bootstrap-form'} do %>
    <div style="position: relative;">
      <div class="form-control title" id="quiz-title-container">
        <% if content_is_locked %>
          <h1 id="quiz_title" class="title"><%= @quiz.title.html_safe %></h1>
          <input type="hidden" name="quiz[title]" value="<%=@quiz.title%>"/>
        <% else %>
          <label for="quiz_title" class="bold form-control__label"><%= t('Quiz Title') %><span aria-hidden="true" class="asterisk"> *</span></label>
          <%= text_field :quiz, :title, :maxlength => "254", :autofocus => true, :required => true,
                         :class => "form-control__input", "aria-describedby" => "quiz_title_message" %>
          <div class="form-control__message input-message__container hidden">
            <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
            <span id="quiz_title_message" class="input-message__text"></span>
          </div>
        <% end %>
      </div>

      <div style="margin: 15px 0 25px;">
        <% if content_is_locked %>
          <div id="quiz_description" class="quiz_description">
            <%= @quiz.description.html_safe %>
          </div>
          <input type="hidden" name="description" value="<%=@quiz.description%>"/>
        <% else %>
          <label for="quiz_description" class="bold"><%= t("Quiz Instructions") %></label>
          <textarea id="quiz_description" style="display: none;width: 100%;margin: 0px auto;height: 125px"><%= @quiz.description %></textarea>
        <% end %>
      </div>

      <fieldset>
        <div class="control-group">
          <label class="control-label" for="quiz_assignment_id"><%= t("Quiz Type") %></label>
          <div class="controls">
            <select name="quiz[quiz_type]" id="quiz_assignment_id">
              <% ['practice_quiz', 'assignment', 'graded_survey', 'survey'].each do |type| %>
                <option value='<%= type %>' <%= 'selected' if @quiz.quiz_type == type %> ><%= render_quiz_type(type) %></option>
              <% end %>
            </select>
          </div>
        </div>

        <div class="control-group">
          <label class="control-label" for="quiz_assignment_group_id"><%= t("Assignment Group") %></label>
          <div class="controls">
            <select name="quiz[assignment_group_id]" id="quiz_assignment_group_id">
              <% current_group_id = @quiz.assignment && @quiz.assignment.assignment_group_id || @quiz.assignment_group_id
                 @context.require_assignment_group %>
              <% @context.assignment_groups.active.each do |g| %>
                <option value="<%= g.id %>" <%= 'selected' if g.id == current_group_id%>><%= g.name %></option>
              <% end %>
            </select>
          </div>
        </div>

        <div class="control-group form-control points-possible" id="quiz_points_possible" style="<%= hidden unless @quiz.graded_survey? %>">
          <label class="control-label form-control__label" for="quiz_points_possible">
            <%= t("Score") %>
          </label>
          <div class="controls">
            <%= text_field :quiz, :points_possible, :class => "form-control__input", :style => "width: 50px;",
                           :required => true, :value => points_possible_display,
                           "aria-describedby" => "graded_survey_points_message" %> pts
            <div class="form-control__message input-message__container" data-original-text="<%= t("Students will automatically receive full credit once they take the survey") %>">
              <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
              <span id="graded_survey_points_message" class="input-message__text">
                <%= t("Students will automatically receive full credit once they take the survey") %>
              </span>
            </div>
          </div>
        </div>
      <% if @context.root_account.suppress_assignments? %>
        <div class="controls">
          <p class="option-caption"><strong><%= t("Grading Visibility") %></strong></p>
          <label class="checkbox" id=suppress_assignment_option>
            <%= check_box :quiz, :suppress_assignment, :checked => !!@quiz.assignment&.suppress_assignment %>
            <%= t("Hide from gradebook view and student grades view") %>
          </label>
        </div>
      <% end %>
        <div class="control-group">
          <div class="controls">
            <p class="option-caption"><strong><%= t("Options") %></strong></p>

            <% if Assignment.sis_grade_export_enabled?(@context) %>
              <label class="checkbox" id=post_to_sis_option>
                <%= check_box :quiz, :post_to_sis, :checked => @quiz.assignment ? @quiz.assignment.post_to_sis : @context.account.sis_default_grade_export[:value] %>
                <%= t("Sync to %{name}", :name => AssignmentUtil.post_to_sis_friendly_name(@context)) %>
              </label>
            <% end %>

            <label class="checkbox" for="quiz_shuffle_answers">
              <%= check_box :quiz, :shuffle_answers %>
              <%= t("Shuffle Answers") %>
            </label>

            <div class="inline-input">
              <label class="checkbox inline nowrap inline-input__label" for="time_limit_option">
                <input type="checkbox" <%= "checked" if @quiz.time_limit %> name="time_limit" id="time_limit_option"/>
                <%= t("Time Limit") %>
              </label>

              <div class="form-control time-limit">
                <div class="form-control__wrapper">
                  <%= text_field :quiz, :time_limit, :id => "quiz_time_limit",
                                 :class => "form-control__input", :style => "width: 30px;",
                                 :"aria-label" => t("How many minutes?"), "aria-describedby" => "time_limit__message" %>
                  <label class="time-limit__label" for="quiz_time_limit">
                    <%= t("Minutes") %>
                  </label>
                </div>

                <div class="form-control__message input-message__container hidden">
                  <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
                  <span id="time_limit__message" class="input-message__text"></span>
                </div>
              </div>
            </div>

            <% if @context.root_account.feature_enabled?(:timer_without_autosubmission) %>
              <label class="checkbox" for="quiz_disable_timer_autosubmission" style="margin-left:12px;<%= hidden if @quiz.time_limit.blank? %>">
                <%= check_box :quiz, :disable_timer_autosubmission %>
                <%= t("Disable Automatic Submission") %>
              </label>
            <% end %>

            <div class="quiz_survey_setting" style="<%= hidden unless @quiz.survey? %>">
              <label class="checkbox" for="quiz_anonymous_submissions">
                <%= check_box :quiz, :anonymous_submissions %>
                <%= t("Keep Submissions Anonymous") %>
              </label>
            </div>

            <!-- Multiple Attempts -->
            <div class="option-group">
              <label class="checkbox" for="multiple_attempts_option">
                <input type="checkbox" <%= "checked" if @quiz.allowed_attempts && @quiz.allowed_attempts != 1 %> name="multiple_attempts" value="1" id="multiple_attempts_option" />
                <%= t("Allow Multiple Attempts") %>
              </label>
              <div class="options" id="multiple_attempts_suboptions">
                <div>
                  <label for="keep_score_select" class="inline"><%= t("Quiz Score to Keep") %></label>
                  <%= select(:quiz, :scoring_policy, [[render_score_to_keep('keep_highest'), 'keep_highest'], [render_score_to_keep('keep_latest'), 'keep_latest'], [render_score_to_keep('keep_average'), 'keep_average']], {}, :id => 'keep_score_select') %>
                </div>
                <div style="display: flex; margin-top:10px">
                  <label for="limit_attempts_option" class="checkbox inline nowrap">
                    <input type="checkbox" <%= "checked" if @quiz.allowed_attempts && @quiz.allowed_attempts > 1 %> name="limit_attempts" id="limit_attempts_option"/>
                    <%= t("Allowed Attempts") %>
                  </label>
                  <div style="display: flex; flex-direction: column">
                    <%= text_field :quiz, :allowed_attempts, :tabindex => "0",
                                   :value => (@quiz.allowed_attempts == -1 ? 1 : @quiz.allowed_attempts),
                                   :"aria-label" => t("Number of allowed attempts"), :class => "form-control__input",
                                   :required => true, "aria-describedby" => "allowed_attempts__message"
                    %>
                    <div style="margin-bottom:10px" class="form-control__message input-message__container hidden" data-original-text="">
                      <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
                      <span id="allowed_attempts__message" class="input-message__text"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Response/CA Visibility -->
            <% if @quiz.restrict_answers_for_concluded_course? %>
            <div class="option-group disabled">
              <label class="checkbox" for="never_hide_results">
                <span data-tooltip title="<%= t('Students cannot view quiz responses after course concludes') %>">
                  <input id="never_hide_results" type="checkbox" disabled="disable" />
                  <%= t("Let Students See Their Quiz Responses (Incorrect Questions Will Be Marked in Student Feedback)") %>
                </span>
              </label>
            </div>
            <% else %>
            <div class="option-group">
              <label class="checkbox" for="never_hide_results">
                <input type="hidden" name="quiz[hide_results][never]" value="0"/>
                <input type="checkbox" name="quiz[hide_results][never]" value="1" id="never_hide_results" <%= 'checked' if @quiz.hide_results != 'always' %> />
                <%= t("Let Students See Their Quiz Responses (Incorrect Questions Will Be Marked in Student Feedback)") %>
              </label>
              <div class="show_quiz_results_options options">
                <div id="hide_results_only_after_last_holder">
                  <label class="checkbox" for="hide_results_only_after_last">
                    <input type="hidden" name="quiz[hide_results][last_attempt]" value="0"/>
                    <input type="checkbox" name="quiz[hide_results][last_attempt]" value="1" id="hide_results_only_after_last" <%= 'checked' if @quiz.hide_results == 'until_after_last_attempt' %> />
                    <%= t("Only After Their Last Attempt") %>
                  </label>
                </div>

                <div>
                  <label class="checkbox" for="quiz_one_time_results">
                    <%= check_box :quiz, :one_time_results %>
                    <%= t("Only Once After Each Attempt") %>
                  </label>
                </div>

                <div>
                  <label class="checkbox" for="quiz_show_correct_answers">
                    <%= check_box :quiz, :show_correct_answers %>
                    <%= t("Let Students See The Correct Answers") %>
                  </label>
                </div>

                <div id="quiz_show_correct_answers_options" class="options">
                  <div>

                    <div id="quiz_show_correct_answers_last_attempt_container">
                      <label class="checkbox" for="quiz_show_correct_answers_last_attempt">
                        <%= check_box :quiz, :show_correct_answers_last_attempt %>
                        <%= t("Only After Their Last Attempt") %>
                      </label>
                    </div>

                    <div class="show-correct-answers form-control">
                      <div class="form-control__wrapper date_field_container inline">
                        <label for="quiz_show_correct_answers_at" class="inline form-control__label">
                          <%= t('Show Correct Answers at') %>
                        </label>

                        <label class="screenreader-only form-control__label" id="show_correct_answers_accessible_label">
                          <%= t("Show Correct Answers At") %>
                          <%= datepicker_screenreader_prompt %>
                        </label>

                        <div class="form-control__inline-wrapper">
                          <%= text_field :quiz, :show_correct_answers_at,
                            :class => "input-medium date_field form-control__input",
                            :type => "text",
                            :value => @quiz.show_correct_answers_at.as_json,
                            "aria-labelledby" => "show_correct_answers_accessible_label",
                            "data-tooltip" =>"",
                            :title => accessible_date_format,
                            "aria-describedby" => "hide_correct_answers_at_message"
                          %>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="hide-correct-answers form-control">
                    <div class="form-control__wrapper date_field_container inline">
                      <label for="quiz_hide_correct_answers_at" class="inline form-control__label">
                        <%= t('Hide Correct Answers at') %>
                      </label>

                      <label class="screenreader-only form-control__label" id="hide_correct_answers_accessible_label">
                        <%= t("Hide Correct Answers At") %>
                        <%= datepicker_screenreader_prompt %>
                      </label>

                      <div class="form-control__inline-wrapper">
                        <%= text_field :quiz, :hide_correct_answers_at,
                          :class => "input-medium date_field",
                          :type => "text",
                          :value => @quiz.hide_correct_answers_at.as_json,
                          "aria-labelledby" => "hide_correct_answers_accessible_label",
                          "data-tooltip" =>"",
                          :title => accessible_date_format,
                          "aria-describedby" => "hide_correct_answers_at_message" %>

                        <div class="form-control__message input-message__container hidden">
                          <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
                          <span id="show_answers__message" class="input-message__text"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <% end %>

            <!-- OQAAT -->
            <div class="option-group">
              <label class="checkbox" for="quiz_one_question_at_a_time">
                <%= hidden_field_tag "quiz[one_question_at_a_time]", "0", :id => nil %>
                <%= check_box_tag "quiz[one_question_at_a_time]", "1", @quiz.one_question_at_a_time?, :class => "element_toggler"  %>
                <%= t('Show one question at a time') %>
              </label>
              <div id="one_question_at_a_time_options" style="<%= 'display:none;' unless @quiz.one_question_at_a_time? %>" class="options">
                <label class="checkbox" for="quiz_cant_go_back">
                  <%= hidden_field_tag "quiz[cant_go_back]", "0", :id => nil %>
                  <%= check_box_tag "quiz[cant_go_back]", "1", @quiz.cant_go_back? %>
                  <%= t("Lock questions after answering") %>
                </label>
              </div>
            </div>

            <p class="option-caption"><strong><%= t("Quiz Restrictions") %></strong></p>

            <!-- Quiz Restrictions -->
            <div class="option-group">
              <label class="checkbox" for="enable_quiz_access_code">
                <input type="checkbox" <%= 'checked' if !@quiz.access_code.blank? %> id="enable_quiz_access_code">
                <%= t("Require an access code") %>
              </label>

              <div class="options control-group form-control access-code screenreader-only">
                <label for="quiz_access_code" class="bold form-control__label">
                  <%= t("Required access code") %><span aria-hidden="true" class="asterisk"> *</span>
                </label>
                <input type="text" tabindex="-1" name="quiz[access_code]" id="quiz_access_code"
                       value="<%= @quiz.access_code %>" aria-describedby="access_code_message"
                       class="form-control__input" />
                <div class="form-control__message input-message__container" data-original-text="<%= t('ex: Password85') %>">
                  <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
                  <span id="access_code_message" class="input-message__text"><%= t('ex: Password85') %></span>
                </div>
              </div>
            </div>
            <div class="option-group">
              <label class="checkbox" for="enable_quiz_ip_filter">
                <input type="checkbox" <%= 'checked' if !@quiz.ip_filter.blank? %> id="enable_quiz_ip_filter">
                <%= t("Filter IP Addresses") %>
              </label>
              <div class="form-control ip-filter options control-group screenreader-only" aria-hidden="<%= !@quiz.ip_filter.blank? %>">
                <label for="quiz_ip_filter" class="bold form-control__label">
                  <%= t("Filter by IP address") %><span aria-hidden="true" class="asterisk"> *</span>
                </label>
                <div class="form-control__wrapper">
                  <input type="text" class="form-control__input" tabindex="-1" name="quiz[ip_filter]"
                         id="quiz_ip_filter" value="<%= @quiz.ip_filter %>" maxlength="255"
                         aria-describedby="filter_ip_message"
                    />
                  <%= link_to(image_tag("find.png", :alt => t('Find IP Address Filter')), '#', :class => 'ip_filtering_link', :title => t('Find IP Address Filter'), 'aria-label' => t('Find IP Address Filter'), :tabindex => -1) %>
                </div>
                <div class="form-control__message input-message__container" data-original-text="<%= t('ex: *************') %>">
                  <i class="input-message__icon icon-warning icon-Solid" aria-hidden="true"></i>
                  <span id="filter_ip_message" class="input-message__text"><%= t('ex: *************') %></span>
                </div>
              </div>
            </div>
            <% if feature_enabled?(:lockdown_browser) && !@quiz.lockdown_browser_use_lti_tool? %>
              <div class="option-group">
                <label class="checkbox" for="quiz_require_lockdown_browser">
                  <%= check_box :quiz, :require_lockdown_browser %>
                  <%= t("Require Respondus LockDown Browser") %>
                </label>
                <div id="lockdown_browser_suboptions" class="options" style="display: none;">
                  <label class="checkbox" for="quiz_require_lockdown_browser_for_results">
                    <%= check_box :quiz, :require_lockdown_browser_for_results %>
                    <%= t("Required to view quiz results") %>
                  </label>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </fieldset>

      <div id="overrides-wrapper">
        <label class="control-label">
          <%= t("Assign") %>
        </label>
        <div class="quiz-overrides-column-right js-assignment-overrides overrideFormFlex">
        </div>
      </div>
      <% if (
        @context.root_account.feature_enabled?(:course_pace_pacing_with_mastery_paths) &&
        ConditionalRelease::Service.enabled_in_context?(@context) &&
        @context.is_a?(Course) && @context.enable_course_paces?
      ) %>
        <div>
          <label class="control-label">
            <%= t("Mastery Paths") %>
          </label>
          <div class="quiz-overrides-column-right js-assignment-overrides-mastery-paths overrideFormFlex">
          </div>
        </div>
      <% end %>
    </div>
  <% end %>
  <%= prefetch_assignment_external_tools %>
</div>
