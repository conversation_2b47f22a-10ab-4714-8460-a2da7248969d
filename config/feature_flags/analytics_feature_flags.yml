---
# Course Analytics - Provided through K20 LTI App
analytics_2:
  state: hidden
  applies_to: Course
  display_name: Course and User Analytics
  description:
    course_analytics_description: Show analytics for course and user data. Data
      may take 24 hours to appear in Course Analytics after setting this feature flag
      to ON.
  after_state_change_proc: analytics_2_after_state_change_hook
  beta: false
hide_legacy_course_analytics:
  state: hidden
  applies_to: Course
  display_name: Hide Legacy Course Analytics
  description:
    hide_legacy_course_analytics_description: Hides the Legacy Course Analytics feature.
  root_opt_in: true
  beta: true
  shadow: true

# Admin Analytics - Provided through K20 LTI App
admin_analytics_view_permission:
  state: hidden
  applies_to: Account
  display_name: Admin Analytics - View admin analytics permission
  description:
    admin_analytics_view_permission_description: Makes the new Admin Analytics permission visible.
  root_opt_in: true
  beta: true
admin_analytics:
  state: allowed
  applies_to: Account
  display_name: Admin Analytics
  description:
    admin_analytics_description: Enables Analytics at the account-level. Admin Analytics dashboards provide
      details on Canvas usage, Canvas course success, and student engagement.
      Learn more in the Admin Analytics Hub in the Canvas community.
  after_state_change_proc: analytics_2_after_state_change_hook
  root_opt_in: true
  beta: false


# Intelligent Insights - Provided through K20 LTI App
k20_course_readiness:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Course Readiness
  description:
    k20_course_readiness: Enables the Intelligent Insights - Course Readiness feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true
k20_course_readiness_due_date_criteria:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Course Readiness - Due Date Criteria
  description:
    k20_course_readiness_due_date_criteria: Enables the Intelligent Insights - Course Readiness - Due Date Criteria feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true
k20_course_readiness_gradebook_criteria:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Course Readiness - Gradebook Criteria
  description:
    k20_course_readiness_gradebook_criteria: Enables the Intelligent Insights - Course Readiness - Gradebook Criteria feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true
k20_course_readiness_broken_links_criteria:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Course Readiness - Broken Links Criteria
  description:
    k20_course_readiness_broken_links_criteria: Enables the Intelligent Insights - Course Readiness - Broken Links Criteria feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true
k20_students_in_need_of_attention:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Students in Need of Attention
  description:
    k20_students_in_need_of_attention: Enables the Intelligent Insights - Students in Need of Attention feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true
title_iv_financial_aid_report:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Financial Aid Compliance
  description:
    title-iv-financial-aid-report: Enables the Intelligent Insights - Financial Aid Compliance feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true

# Intelligent Insights - Provided through Ask Your Data LTI App
advanced_analytics_ask_questions:
  state: hidden
  applies_to: Account
  display_name: Intelligent Insights - Ask Your Data
  description:
    advanced_analytics_ask_questions: Enables the Intelligent Insights - Ask Your Data feature and accompanying permission.
  root_opt_in: true
  beta: true
  shadow: true

# Analytics Hub
analytics_hub:
  state: hidden
  applies_to: Account
  display_name: "Analytics Hub"
  description: This feature provides a new Analytics Hub platform experience.
  root_opt_in: true
  beta: true
  shadow: true
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on

# Admin Analytics - Contextual Messaging
intelligent_insights_contextual_messaging:
  state: hidden
  applies_to: Account
  display_name: "Intelligent Insights - Contextual Messaging"
  description: Show Intelligent Insights new contextual messaging feature.
  root_opt_in: true
  beta: true
  shadow: true
