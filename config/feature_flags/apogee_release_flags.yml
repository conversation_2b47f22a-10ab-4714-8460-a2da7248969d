---
missing_policy_applicator_emits_live_events:
  state: hidden
  applies_to: RootAccount
  display_name: Missing Policy Applicator Emits Live Events
  description:
    Allows the missing policy applicator to emit submission_updated live events for each submission
    marked as missing.
disable_post_to_sis_when_grading_period_closed:
  state: hidden
  applies_to: RootAccount
  display_name: Disable Post to SIS for Assignment in Closed Grading Periods
  description:
    If set, turns off post-to-SIS functionality for assignments when their grading period closes.
    Only applies if the "Enable new SIS integration settings" feature is enabled.
responsive_student_grades_page:
  state: hidden
  applies_to: RootAccount
  display_name: Responsive Student Grades Page
  description: Makes the student grades page responsive.
view_ungraded_as_zero:
  type: setting
  state: hidden
  applies_to: Account
  display_name: View Ungraded as Zero View in Gradebook
  description:
    The Gradebook will factor in ungraded submissions as if they were given a score of zero for
    calculations. This is just a view for the teacher, and does not affect actual scoring.
grade_calc_ignore_unposted_anonymous:
  state: hidden
  applies_to: RootAccount
  display_name: Ignore Unposted Anonymous Assignments in Grade Calculation
  description:
    If set, the grade calculator will ignore unposted anonymous assignments when calculating
    totals.
  environments:
    ci:
      state: on # enable for automated testings builds and local testing
    development:
      state: on # enable for local development
    test:
      state: on # enable for the deployed 'test' environment
enhanced_gradebook_filters:
  beta: true
  state: allowed
  applies_to: Course
  root_opt_in: true
  display_name: Enhanced Gradebook Filters
  description:
    wrapper: <a href="https://community.canvaslms.com/t5/Gradebook-Filters/gh-p/Gradebook-Filters">\1</a>
    enhanced_gradebook_filters_description:
      This feature provides enhanced filtering options in the Canvas gradebook. When
      this flag is enabled, early access to ongoing improvements will be displayed
      automatically with every release. For full details, please see the *Gradebook
      Filters User Group* in the Canvas Community.
  environments:
    development:
      state: on # enable for local development
message_observers_of_students_who:
  state: hidden
  applies_to: SiteAdmin
  display_name: Message Observers of Students Who...
  description: Enables a redesigned Message Students Who... dialog that allows
    for messaging observers in addition to students.
apply_score_to_ungraded:
  state: hidden
  applies_to: Account
  display_name: Apply Score to Ungraded
  description:
    Allows instructors to apply scores to ungraded submissions en masse from assignment
    group and Total column menus in the Gradebook.
  environments:
    development:
      state: allowed
enhanced_grade_statistics:
  state: hidden
  applies_to: SiteAdmin
  display_name: Show quartiles for grades and use them for box and whiskers
  description:
    Updates the student-facing assignment statistics to include quartiles and makes the
    box-and-whiskers plot a proper box and whiskers plot.
submission_comment_emojis:
  state: hidden
  display_name: Emojis in Submission Comments
  description:
    Adds an emoji picker that allows students and teachers to add emojis to
    their submission comments.
  applies_to: Course
  root_opt_in: true
  environments:
    ci:
      state: allowed_on # enable for automated testings builds and local testing
    development:
      state: allowed_on # enable for local development
peer_reviews_for_a2:
  state: hidden
  applies_to: Course
  display_name: Peer Review Support in Assignment Enhancements
  description:
    Students will be able to provide feedback on another student's assignment submission
    using the enhanced assignment view
assignment_missing_shortcut:
  state: hidden
  applies_to: SiteAdmin
  display_name: Keyboard Shortcut for Missing Assignment
  description: Allows graders to type MI in the Gradebook or SpeedGrader to manually tag an assignment as missing
originality_reports_for_a2:
  state: hidden
  applies_to: SiteAdmin
  display_name: Originality Reports with Assignment Enhancements
  description: If set, displays originality reports with Assignment Enhancements turned on
  environments:
    ci:
      state: on # enable for automated testings builds and local testing
    development:
      state: on # enable for local development
    test:
      state: on # enable for the deployed 'test' environment
single_new_quiz_session_in_speedgrader:
  state: hidden
  applies_to: SiteAdmin
  display_name: Single Quizzes.Next Session in SpeedGrader
  description: If set, Quizzes.Next will operate using a single launch session in SpeedGrader
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
new_quizzes_grade_by_question_in_speedgrader:
  state: hidden
  applies_to: SiteAdmin
  display_name: Quizzes.Next Grade by Question in SpeedGrader
  description: Adds the option to grade by question in SpeedGrader for Quizzes.Next Quizzes
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on
proxy_file_uploads:
  state: hidden
  applies_to: SiteAdmin
  display_name: Submit on Behalf of Student
  description:
    Allows teachers with the proper permissions to submit file upload assignments
    on behalf of their students.
  environments:
    ci:
      state: allowed_on # enable for automated testings builds and local testing
deprioritize_section_overrides_for_nonactive_enrollments:
  state: hidden
  applies_to: SiteAdmin
  display_name: Deprioritize Section Overrides for Nonactive Enrollments
  description:
    If set, deprioritizes using due dates, lock dates, and unlock dates from
    section overrides on an assignment that are associated with a deactivated, concluded, or
    otherwise non-active enrollment for a student.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
additional_speedgrader_links:
  state: hidden
  applies_to: SiteAdmin
  display_name: Add SpeedGrader links to Assignments and Quizzes index views
  description: Reorders some menu options for more consistency and adds link
    to SpeedGrader.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
new_quiz_deleted_workflow_restore_pending_review_state:
  state: hidden
  applies_to: SiteAdmin
  display_name: New Quiz deleted user restore pending review state on enroll
  description:
    When set, determines if the workflow state of a new quiz should be
    set as graded or pending_review if a user had a previous pending_review new quiz but
    had been re-enrolled in the course.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
hide_zero_point_quizzes_option:
  state: hidden
  applies_to: SiteAdmin
  display_name: Hide zero point new quizzes from the student and teacher gradebooks
  description:
    When set, an option to not display a new quiz with 0 points possible in the gradebook
    will be available in the form of a checkbox when creating or editing the quiz.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
student_grade_summary_upgrade:
  state: hidden
  display_name: Update grade summary table to use a modern framework
  description: |-
    This refactor will make this page more maintainable
  applies_to: SiteAdmin
fix_missing_policy_applicator_gradebook_history:
  state: hidden
  applies_to: SiteAdmin
  display_name: Fix Missing Policy Applicator Gradebook History
  description:
    Fixes the missing policy applicator to insert records into Gradebook History
    for each submission marked as missing.
  environments:
    ci:
      state: allowed_on # enable for automated testings builds and local testing
    development:
      state: allowed_on # enable for local development
    test:
      state: allowed_on # enable for the deployed 'test' environment
grading_scheme_updates:
  state: hidden
  applies_to: SiteAdmin
  display_name: Improve Letter Grading Scheme Usability
  description: Teachers and Account Managers will be able to more easily manage letter grading schemes for accounts, courses, and assignments.
  environments:
    development:
      state: allowed_on # enable by deault for local development
custom_gradebook_statuses:
  state: hidden
  applies_to: SiteAdmin
  display_name: Custom Status Labels for Submissions and Final Grades
  description: Account administrators can create custom status labels that instructors will be able to use in their gradebooks. These custom status labels will not have any functionality associated with them like missing or late. Admins will also be able to disable default status labels and prevent them from being used in the entire instance.
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on
submission_stickers:
  state: hidden
  applies_to: Course
  display_name: Submission Stickers
  description: Allows teachers to attach stickers to student submissions. Requires the "Assignment Enhancements - Student" feature flag to be enabled.
  custom_transition_proc: assignment_enhancements_prereq_for_stickers_hook
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
anonymous_grading_with_new_quizzes:
  state: hidden
  applies_to: SiteAdmin
  display_name: Anonymous Grading with New Quizzes
  description: Allows instructors to enable anonymous grading for New Quizzes.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
default_account_grading_scheme:
  state: hidden
  applies_to: SiteAdmin
  display_name: Default Grading Scheme for Accounts
  description: Allow administrators to set a default grading scheme for accounts. This grading scheme will be the default for all courses in the account, but individual courses can overwrite it with their own default.
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
external_tools_for_a2:
  state: hidden
  display_name: External tools in Assignment Enhancements
  description: Allow for Assignment Enhancements to be used for external LTI tools
  applies_to: SiteAdmin
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development
archived_grading_schemes:
  state: hidden
  applies_to: SiteAdmin
  display_name: Archived Grading Schemes
  description: Users can now archive grading schemes, preventing them from being added to courses/assignments. Grading schemes in old contexts where it is being used will continue being used. Archived grading schemes can be accessed on the account level grading scheme settings page, but cannot be edited or deleted.
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on
enhanced_rubrics:
  state: hidden
  beta: true
  applies_to: Course
  root_opt_in: true
  display_name: Enhanced Rubrics
  description: This feature provides visual and functional enhancements to rubrics across Canvas.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
enhanced_rubrics_copy_to:
  state: hidden
  applies_to: SiteAdmin
  root_opt_in: true
  display_name: Enhanced Rubrics Copy To
  description: This functionality allows copying rubrics between courses from the enhanced rubrics UI and also assigning them directly to an assignment.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
multiselect_gradebook_filters:
  state: hidden
  applies_to: SiteAdmin
  display_name: Multi-select Gradebook Filters
  description: This feature enables multi-select filters in the Gradebook.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
customizable_default_due_time:
  state: hidden
  applies_to: SiteAdmin
  display_name: Customizable Default Due Date
  description: Users can now set the default due date for a course to be in minutes
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on
platform_service_speedgrader:
  state: hidden
  applies_to: Course
  display_name: Performance and Usability Upgrades for SpeedGrader
  description: This feature provides a new SpeedGrader platform experience with a focus on improved performance for large courses.
  after_state_change_proc: log_modernized_speedgrader_metrics
  environments:
    ci:
      state: allowed
    development:
      state: allowed
moderated_grading_modernized_speedgrader:
  state: hidden
  display_name: Performance and Usability Updates for Moderated Grading in SpeedGrader
  description: This feature provides the new SpeedGrader platform experience with a focus on improved performance for large courses for Moderated Grading assignments
  applies_to: SiteAdmin
  environments:
    ci:
      state: allowed
    development:
      state: allowed
grading_periods_filter_dates:
  state: hidden
  applies_to: SiteAdmin
  display_name: Add dates to Gradebook Grading Period Filter
  description: This feature adds the start, end, and close dates of grading periods to the grading period filter.
  environments:
    ci:
      state: allowed_on
    development:
      state: allowed_on
speedgrader_studio_media_capture:
  state: hidden
  applies_to: SiteAdmin
  display_name: SpeedGrader Studio Media Capture
  description: This feature provides the ability to capture media in SpeedGrader using Studio Screen Capture.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
rubric_imports_exports:
  state: hidden
  applies_to: SiteAdmin
  root_opt_in: true
  display_name: Rubric Imports and Exports
  description: This feature allows users to import and export rubrics from CSV and XML files from Enhanced Rubrics.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
rce_lite_enabled_speedgrader_comments:
  state: hidden
  applies_to: RootAccount
  display_name: RCE lite in Speedgrader comments
  description: Allows Speedgrader comments to be formatted using RCE lite.
enhanced_rubrics_assignments:
  state: hidden
  beta: true
  applies_to: SiteAdmin
  root_opt_in: true
  display_name: Enhanced Rubrics Assignments Page
  description: This feature allows users to create and search for rubrics while using the Enhanced Rubrics design on the Assignments page.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
speedgrader_grade_by_student:
  state: hidden
  beta: true
  applies_to: RootAccount
  display_name: SpeedGrader Grade by Student
  description: Allow instructors to view and traverse between students and assignments from within SpeedGrader
rubric_assessment_imports_exports:
  state: hidden
  applies_to: SiteAdmin
  root_opt_in: true
  display_name: Rubric Assessments Imports and Exports
  description: This feature allows users to import and export rubrics assessments from Gradebook in a CSV format. This feature is dependent on Enhanced Rubrics feature.
  environments:
    ci:
      state: on
    development:
      state: on
    test:
      state: on
rubric_self_assessment:
  state: hidden
  applies_to: RootAccount
  root_opt_in: true
  display_name: Rubric Self Assessments
  description: This feature allows rubric self assessments to be enabled for an assignment. Students can assess their own submission using the assignment rubric.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
ai_rubrics:
  state: hidden
  applies_to: Course
  root_opt_in: true
  beta: true
  display_name: AI Generated Rubrics
  description: Give rubric creators the option to generate rubric criteria automatically based on an assignment description via a large language model (LLM).
use_body_word_count:
  state: allowed_on # this is intentional. we want to enable this upon deploy so we start populating word count.
  applies_to: SiteAdmin
  display_name: Use body_word_count for displaying submission word count
  description: For text entry submisisons, the word count is currently calculated on-the-fly which can lead to load time issues. With this flag enabled, the value will be read from the new body_word_count column.
graphql_honor_anonymous_grading:
  state: hidden
  applies_to: SiteAdmin
  display_name: GraphQL API Honors Anonymous Grading
  description: GraphQL API will return anonymous data for assignments and courses with anonymous grading enabled.
  environments:
    ci:
      state: on
    development:
      state: on
submission_comment_media_auto_captioning:
  state: allowed_on
  applies_to: Account
  display_name: Auto Captioning of Media Submission Comments
  description: This feature enables automatic captioning of media submission comments.
  environments:
    ci:
      state: allowed
    development:
      state: allowed
post_grades_enhanced_modal:
  state: hidden
  display_name: Post Grades Enhanced Modal
  description: Accessible refactor of Sync Grades dialog
  applies_to: Course
  root_opt_in: true
project_lhotse:
  state: hidden
  applies_to: Course
  display_name: Grading Assistance
  description: Enables the new AI-assisted grading tool inside of the Modernized Speedgrader for assignments in this course.
  hidden: true
performance_improvements_for_gradebook:
  state: hidden
  applies_to: Course
  display_name: Performance Improvements for Gradebook Data Loading
  description: Reduce Gradebook load time by improving back end performance and reducing unnecessary background data
  shadow: true
  environments:
    ci:
      state: allowed_on # enable for automated testings builds and local testing
    development:
      state: allowed_on # enable for local development
grading_assistance_file_uploads:
  state: hidden
  applies_to: SiteAdmin
  display_name: Grading Assistance File Uploads
  description: Enables support for uploading files as part of the AI-assisted grading tool in the Modernized Speedgrader.
  shadow: true
