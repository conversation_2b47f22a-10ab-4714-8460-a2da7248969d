---
confetti_for_assignments:
  type: setting
  state: hidden
  display_name: <PERSON><PERSON>tti for On-Time Submissions
  description: |-
    Displays confetti for users that turn in an assignment prior
    to the set due date.
  applies_to: RootAccount

confetti_for_valid_links:
  type: setting
  state: hidden
  display_name: Confetti for valid links
  description: |-
    Displays confetti for teachers that run the link validator on
    a course and no issues are found.
  applies_to: RootAccount

bbb_avatar:
  state: 'hidden'
  display_name: 'Send user avatar to BigBlueButton'
  description: |-
    Adds a new admin setting to the BigBlueButton plugin to allow
    sending of user avatars upon joining conference.
  applies_to: RootAccount

react_discussions_post:
  state: 'on'
  beta: true
  display_name: Discussions/Announcements Redesign
  description:
    wrapper: <a href="https://community.canvaslms.com/t5/Discussions-Announcements/gh-p/discussions">\1</a>
    discussion_redesign_description: This feature enhances the Canvas experience
      within Discussions and Announcements. When this flag is enabled, early access
      to ongoing improvements will be displayed automatically with every release.
      For full details, please see the *Discussions/Announcements Redesign user group*
      in the Canvas Community
  applies_to: Course

discussions_reporting:
  state: hidden
  beta: true
  display_name: Discussions Redesign - Reporting
  description: |-
    This feature flag enables Discussions Reporting inside the new
    Discussions Redesign work.
  applies_to: RootAccount

draft_discussions:
  state: hidden
  beta: true
  display_name: Draft Discussion Entries
  description: |-
    Will allow for saving drafts as students type their replies to a discussion topic
    in the discussions redesign
  applies_to: SiteAdmin

discussion_grading_view:
  state: hidden
  beta: true
  display_name: Discussion Redesign Grading View
  description: |-
    Will allow for graders to grade assigned discussions without leaving discussion page
  applies_to: SiteAdmin

discussion_entry_version_history:
  state: hidden
  display_name: Discussion Redesign - Discussion Entry Version History
  description: |-
    Will display the version history for edited discussion entries within the Redesign UI
  applies_to: SiteAdmin

discussion_create:
  state: hidden
  display_name: Discussion Create/Edit Page React Update
  description: |-
    This feature flag enables the use of the updated
    discussion create/edit page
  applies_to: RootAccount

react_inbox_labels:
  state: hidden
  display_name: Enable Labels on the new Inbox page
  description: |-
    This will allow the user to manage their labels, add labels to conversations and
    filter conversations by labels.
  applies_to: SiteAdmin

restrict_quantitative_data:
  state: hidden
  display_name: Restrict quantitative data
  description: |-
    This feature will hide all quantitative values from users
  applies_to: RootAccount

smart_alerts:
  state: hidden
  display_name: Smart alerts
  description: |-
    Notify students in time of assignments that require their attention.
  applies_to: RootAccount

show_push_notification_account_setting:
  state: hidden
  display_name: Allow push notification opt-out
  description: |-
    Enable an account setting which toggles push notifications for that account
  applies_to: SiteAdmin

apollo_caching:
  state: hidden
  display_name: Apollo caching
  description: |-
    Allow for Apollo to cache graphQL queries on a users browser to speed up their experience
  applies_to: SiteAdmin

react_people_page:
  state: hidden
  display_name: People Page Upgrade
  description: |-
    This feature flag enables the React/InstUI and GraphQL People page upgrade.
  applies_to: RootAccount

discussion_checkpoints:
  state: allowed
  display_name: Discussion Checkpoints
  description: |-
    Enables checkpoints for graded discussions. Checkpoints allow teachers to
    create separate due dates for the initial response and replies. The initial
    response and replies are then graded separately, and those grades are
    combined to form a final discussion grade.
  applies_to: Account
  environments:
    ci:
      state: allowed_on # enable for automated testing builds and local testing
    development:
      state: allowed_on # enable for local development

inbox_settings:
  state: hidden
  applies_to: SiteAdmin
  display_name: Inbox Settings - QW Updates
  description: Gives users access to new optional features like setting a default signature that will be added at the end of outgoing messages and setting an autoresponder.

disallow_threaded_replies_fix_alert:
  state: hidden
  applies_to: SiteAdmin
  display_name: Disallow Threaded Replies Fix Alert
  description: |-
    This feature flag controls the visibility of the alert that allows teachers to fix threaded replies on the Discussions Index page.

discussion_permalink:
  state: hidden
  applies_to: SiteAdmin
  display_name: Discussion permalink
  description: |-
    This feature flag enables the Copy Link option in the discussions redesign

discussion_insights:
  state: hidden
  applies_to: Course
  display_name: Discussion Insights
  description: |-
    Allow access to discussion insights

discussion_ai_survey_link:
  state: hidden
  applies_to: SiteAdmin
  display_name: Discussion AI survey link
  description: |-
    Displays a survey link for Discussion Translate and Discussion Insights

disallow_threaded_replies_manage:
  state: hidden
  applies_to: SiteAdmin
  display_name: Manage Disallow Threaded Replies in Discussions
  description: |-
    This feature flag controls the visibility of the alert that allows teachers to manage threaded replies for each discussion topic on the Discussions Index page, this will override the Dissalow Threaded Replies Fix Alert.


discussion_pin_post:
  state: hidden
  applies_to: Course
  display_name: Discussion Pin Post
  description: |-
    This feature flag controls the ability to pin posts on the Discussions page.
  shadow: true

create_conversation_graphql_rate_limit:
  state: hidden
  applies_to: SiteAdmin
  display_name: Rate Limit for Create Conversation GraphQL Mutation
  description: |-
    This feature flag enables a rate limit for the Create Conversation GraphQL mutation.
    Assigns different scores for different user groups like students, teachers, etc.
    Once the score reaches a certain threshold, the user will be rate limited for a period of time.

