PATH
  remote: .
  specs:
    adheres_to_policy (0.0.1)
      activesupport (< 8.0)

GEM
  remote: https://rubygems.org/
  specs:
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    diff-lcs (1.6.2)
    docile (1.4.1)
    drb (2.2.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    logger (1.7.0)
    minitest (5.25.5)
    rake (13.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  adheres_to_policy!
  rake
  rspec (~> 3.12)
  simplecov (~> 0.22)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
