PATH
  remote: .
  specs:
    canvas_dynamodb (0.0.1)
      aws-sdk-applicationautoscaling (~> 1.26)
      aws-sdk-dynamodb (~> 1.32)
      benchmark (~> 0.4)

GEM
  remote: https://rubygems.org/
  specs:
    aws-eventstream (1.3.2)
    aws-partitions (1.1095.0)
    aws-sdk-applicationautoscaling (1.103.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.218.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-dynamodb (1.142.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    diff-lcs (1.6.2)
    docile (1.4.1)
    jmespath (1.6.2)
    rake (13.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  canvas_dynamodb!
  rake
  rspec (~> 3.12)
  simplecov (~> 0.22)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
