PATH
  remote: ../canvas_cache
  specs:
    canvas_cache (0.1.0)
      activesupport
      config_file
      digest-murmurhash (>= 1.1.0)
      guardrail (>= 2.0.0)
      redis (~> 5.0)
      redis-clustering (~> 5.0)
      redis-scripting (>= 1.0.0)

PATH
  remote: ../canvas_http
  specs:
    canvas_http (1.0.0)
      canvas_cache
      legacy_multipart
      logger (~> 1.5)

PATH
  remote: ../canvas_slug
  specs:
    canvas_slug (0.0.1)
      swearjar (~> 1.4)

PATH
  remote: ../canvas_sort
  specs:
    canvas_sort (1.0.0)

PATH
  remote: ../config_file
  specs:
    config_file (0.1.0)
      railties (>= 5.0)

PATH
  remote: ../legacy_multipart
  specs:
    legacy_multipart (0.0.1)
      base64
      canvas_slug
      mime-types (~> 3.2)

PATH
  remote: .
  specs:
    canvas_kaltura (1.0.0)
      canvas_http
      canvas_slug
      canvas_sort
      csv
      legacy_multipart
      nokogiri

GEM
  remote: https://rubygems.org/
  specs:
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.5)
    date (3.4.1)
    diff-lcs (1.6.2)
    digest-murmurhash (1.1.1)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    guardrail (3.0.4)
      activerecord (>= 6.1, < 8.0)
      railties (>= 6.1, < 8.0)
    hashdiff (1.1.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0429)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-cluster-client (0.13.4)
      redis-client (~> 0.24)
    redis-clustering (5.4.0)
      redis (= 5.4.0)
      redis-cluster-client (>= 0.10.0)
    redis-scripting (1.0.1)
      redis (>= 3.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rexml (3.4.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    stringio (3.1.7)
    swearjar (1.4.0)
    thor (1.3.2)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    useragent (0.16.11)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  canvas_cache!
  canvas_http!
  canvas_kaltura!
  canvas_slug!
  canvas_sort!
  config_file!
  legacy_multipart!
  rake
  rspec (~> 3.12)
  webmock (~> 3.18)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
