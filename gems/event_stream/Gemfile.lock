PATH
  remote: ../bookmarked_collection
  specs:
    bookmarked_collection (1.0.0)
      activerecord (>= 3.2)
      folio-pagination (~> 0.0.12)
      json_token
      paginated_collection
      railties (>= 3.2)
      will_paginate (>= 3.0, < 5.0)

PATH
  remote: ../config_file
  specs:
    config_file (0.1.0)
      railties (>= 5.0)

PATH
  remote: ../json_token
  specs:
    json_token (0.0.1)
      base64
      json

PATH
  remote: ../paginated_collection
  specs:
    paginated_collection (1.0.0)
      folio-pagination (~> 0.0.12)
      will_paginate (>= 3.0, < 5.0)

PATH
  remote: .
  specs:
    event_stream (0.1.0)
      activerecord (>= 4.2)
      bookmarked_collection
      inst_statsd
      json_token
      paginated_collection

GEM
  remote: https://rubygems.org/
  specs:
    actionpack (7.2.2.1)
      actionview (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actionview (7.2.2.1)
      activesupport (= 7.2.2.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activerecord (7.2.2.1)
      activemodel (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      timeout (>= 0.4.0)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    aroi (1.0.0)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    diff-lcs (1.6.2)
    docile (1.4.1)
    dogstatsd-ruby (5.7.0)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    folio-pagination (0.0.12)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inst_statsd (3.4.0)
      aroi (>= 0.0.7)
      dogstatsd-ruby (>= 4.2, < 6.0, != 5.0.0)
      statsd-ruby (~> 1.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.13.0)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.2.2.1)
      actionpack (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    sqlite3 (2.6.0)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.6.0-aarch64-linux-gnu)
    sqlite3 (2.6.0-arm64-darwin)
    sqlite3 (2.6.0-x86_64-darwin)
    sqlite3 (2.6.0-x86_64-linux-gnu)
    statsd-ruby (1.5.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    useragent (0.16.11)
    will_paginate (4.0.1)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  bookmarked_collection!
  config_file!
  event_stream!
  json_token!
  paginated_collection!
  rspec (~> 3.12)
  simplecov (~> 0.22)
  sqlite3

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
