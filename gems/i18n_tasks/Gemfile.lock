PATH
  remote: ../i18n_extraction
  specs:
    i18n_extraction (0.0.1)
      activesupport
      i18nliner (~> 0.1)
      ruby_parser (~> 3.7)
      sexp_processor (~> 4.14, >= 4.14.1)

PATH
  remote: ../utf8_cleaner
  specs:
    utf8_cleaner (0.0.1)

PATH
  remote: .
  specs:
    i18n_tasks (0.0.1)
      activesupport
      csv
      i18n (>= 0.7, < 2)
      i18n_extraction
      ruby_parser (~> 3.7)
      utf8_cleaner

GEM
  remote: https://rubygems.org/
  specs:
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    csv (3.3.5)
    diff-lcs (1.6.2)
    docile (1.4.1)
    drb (2.2.3)
    erubi (1.13.1)
    globby (0.1.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18nliner (0.2.4)
      activesupport (>= 6.0)
      erubi (~> 1.7)
      globby (>= 0.1.1)
      i18n (>= 1.8.6)
      nokogiri (>= 1.5.0)
      ruby2ruby (~> 2.4)
      ruby_parser (~> 3.10)
      sexp_processor (~> 4.10)
      ya2yaml (= 0.31)
    logger (1.7.0)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    racc (1.8.1)
    rake (13.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    ruby2ruby (2.5.2)
      ruby_parser (~> 3.1)
      sexp_processor (~> 4.6)
    ruby_parser (3.21.1)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    securerandom (0.4.1)
    sexp_processor (4.17.3)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    ya2yaml (0.31)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  i18n_extraction!
  i18n_tasks!
  i18nliner (~> 0.2.4)
  rake
  rspec (~> 3.12)
  simplecov (~> 0.22)
  utf8_cleaner!

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
