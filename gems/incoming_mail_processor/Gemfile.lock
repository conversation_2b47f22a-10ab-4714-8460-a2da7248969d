PATH
  remote: ../canvas_errors
  specs:
    canvas_errors (0.1.0)
      activesupport
      code_ownership
      inst-jobs

PATH
  remote: ../canvas_text_helper
  specs:
    canvas_text_helper (0.0.1)
      i18n

PATH
  remote: ../html_text_helper
  specs:
    html_text_helper (0.0.1)
      activesupport
      canvas_text_helper
      nokogiri
      sanitize (~> 7.0)
      twitter-text (~> 3.1)

PATH
  remote: ../utf8_cleaner
  specs:
    utf8_cleaner (0.0.1)

PATH
  remote: .
  specs:
    incoming_mail_processor (0.0.1)
      activesupport (>= 3.2)
      aws-sdk-s3
      aws-sdk-sqs
      canvas_errors
      html_text_helper
      inst_statsd
      mail (~> 2.8)
      net-imap
      net-pop
      net-smtp
      utf8_cleaner

GEM
  remote: https://rubygems.org/
  specs:
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-pg-extensions (0.6.0)
      activerecord (>= 7.0, < 8.1)
      railties (>= 7.0, < 8.1)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    after_transaction_commit (2.2.2)
      activerecord (>= 5.2)
    aroi (1.0.0)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1095.0)
    aws-sdk-core (3.218.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.184.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-sqs (1.93.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    builder (3.3.0)
    code_ownership (1.39.0)
      code_teams (~> 1.0)
      packs-specification
      sorbet-runtime (>= 0.5.11249)
    code_teams (1.1.0)
      sorbet-runtime
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    diff-lcs (1.6.2)
    dogstatsd-ruby (5.7.0)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    idn-ruby (0.1.5)
    inst-jobs (********)
      activerecord (>= 7.0)
      activerecord-pg-extensions (~> 0.4)
      activesupport (>= 7.0)
      after_transaction_commit (>= 1.0, < 3)
      debug_inspector (~> 1.0)
      fugit (~> 1.3)
      railties (>= 6.0)
    inst_statsd (3.4.0)
      aroi (>= 0.0.7)
      dogstatsd-ruby (>= 4.2, < 6.0, != 5.0.0)
      statsd-ruby (~> 1.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    packs-specification (0.0.10)
      sorbet-runtime
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    securerandom (0.4.1)
    sorbet-runtime (0.5.12216)
    statsd-ruby (1.5.0)
    stringio (3.1.7)
    thor (1.3.2)
    timecop (0.9.10)
    timeout (0.4.3)
    twitter-text (3.1.0)
      idn-ruby
      unf (~> 0.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (0.0.9.1)
    useragent (0.16.11)
    webrick (1.9.1)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  canvas_errors!
  canvas_text_helper!
  debug
  html_text_helper!
  incoming_mail_processor!
  rspec (~> 3.12)
  timecop (~> 0.9.5)
  utf8_cleaner!
  webrick

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
