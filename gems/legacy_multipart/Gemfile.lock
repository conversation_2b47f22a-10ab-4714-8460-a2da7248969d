PATH
  remote: ../canvas_slug
  specs:
    canvas_slug (0.0.1)
      swearjar (~> 1.4)

PATH
  remote: .
  specs:
    legacy_multipart (0.0.1)
      base64
      canvas_slug
      mime-types (~> 3.2)

GEM
  remote: https://rubygems.org/
  specs:
    base64 (0.3.0)
    diff-lcs (1.6.2)
    logger (1.7.0)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0429)
    rack (2.2.9)
    rack-test (2.2.0)
      rack (>= 1.3)
    rake (13.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    swearjar (1.4.0)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  canvas_slug!
  legacy_multipart!
  rack (~> 2.2)
  rack-test (~> 2.1)
  rake
  rspec (~> 3.12)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
