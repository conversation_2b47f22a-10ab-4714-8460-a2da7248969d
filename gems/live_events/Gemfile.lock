PATH
  remote: .
  specs:
    live_events (1.0.0)
      activesupport
      aws-sdk-kinesis
      inst_statsd

GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activerecord (7.2.2.1)
      activemodel (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      timeout (>= 0.4.0)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    aroi (1.0.0)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1095.0)
    aws-sdk-core (3.218.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kinesis (1.77.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.6.2)
    dogstatsd-ruby (5.7.0)
    drb (2.2.3)
    erb (5.0.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inst_statsd (3.4.0)
      aroi (>= 0.0.7)
      dogstatsd-ruby (>= 4.2, < 6.0, != 5.0.0)
      statsd-ruby (~> 1.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    logger (1.7.0)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    racc (1.8.1)
    rake (13.2.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    statsd-ruby (1.5.0)
    stringio (3.1.7)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  debug
  live_events!
  nokogiri
  rake
  rspec (~> 3.12)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
