PATH
  remote: .
  specs:
    lti-advantage (0.1.0)
      activemodel (>= 5.1)
      json-jwt (~> 1.5)

GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    aes_key_wrap (1.1.0)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindata (2.5.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    diff-lcs (1.6.2)
    drb (2.2.3)
    faraday (2.13.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    json (2.13.0)
    json-jwt (1.16.7)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    logger (1.7.0)
    minitest (5.25.5)
    net-http (0.6.0)
      uri
    rake (13.2.1)
    redcarpet (3.6.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (1.0.3)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  lti-advantage!
  rake
  redcarpet
  rspec (~> 3.0)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
