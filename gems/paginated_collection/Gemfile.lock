PATH
  remote: .
  specs:
    paginated_collection (1.0.0)
      folio-pagination (~> 0.0.12)
      will_paginate (>= 3.0, < 5.0)

GEM
  remote: https://rubygems.org/
  specs:
    diff-lcs (1.6.2)
    folio-pagination (0.0.12)
    mini_portile2 (2.8.9)
    rake (13.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    sqlite3 (2.6.0)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.6.0-aarch64-linux-gnu)
    sqlite3 (2.6.0-arm64-darwin)
    sqlite3 (2.6.0-x86_64-darwin)
    sqlite3 (2.6.0-x86_64-linux-gnu)
    will_paginate (4.0.1)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  paginated_collection!
  rake
  rspec (~> 3.12)
  sqlite3

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
