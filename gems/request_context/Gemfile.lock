PATH
  remote: ..
  specs:
    canvas_cache (0.1.0)
      activesupport
      config_file
      digest-murmurhash (>= 1.1.0)
      guardrail (>= 2.0.0)
      redis (~> 5.0)
      redis-clustering (~> 5.0)
      redis-scripting (>= 1.0.0)
    canvas_errors (0.1.0)
      activesupport
      code_ownership
      inst-jobs
    canvas_security (0.1.0)
      activesupport
      canvas_cache
      canvas_errors
      dynamic_settings
      json-jwt
    config_file (0.1.0)
      railties (>= 5.0)
    dynamic_settings (0.1.0)
      activesupport (>= 5.0)
      config_file
      diplomat (>= 2.5.1)
      logger (~> 1.5)
      railties

PATH
  remote: .
  specs:
    request_context (0.1.0)
      actionpack
      canvas_security
      railties

GEM
  remote: https://rubygems.org/
  specs:
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-pg-extensions (0.6.0)
      activerecord (>= 7.0, < 8.1)
      railties (>= 7.0, < 8.1)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    aes_key_wrap (1.1.0)
    after_transaction_commit (2.2.2)
      activerecord (>= 5.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindata (2.5.1)
    builder (3.3.0)
    code_ownership (1.39.0)
      code_teams (~> 1.0)
      packs-specification
      sorbet-runtime (>= 0.5.11249)
    code_teams (1.1.0)
      sorbet-runtime
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    deep_merge (1.2.2)
    diff-lcs (1.6.2)
    digest-murmurhash (1.1.1)
    diplomat (2.6.4)
      deep_merge (~> 1.2)
      faraday (>= 0.9, < 3.0, != 2.0.0)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    faraday (2.13.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    guardrail (3.0.4)
      activerecord (>= 6.1, < 8.0)
      railties (>= 6.1, < 8.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inst-jobs (********)
      activerecord (>= 7.0)
      activerecord-pg-extensions (~> 0.4)
      activesupport (>= 7.0)
      after_transaction_commit (>= 1.0, < 3)
      debug_inspector (~> 1.0)
      fugit (~> 1.3)
      railties (>= 6.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.13.0)
    json-jwt (1.16.7)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    net-http (0.6.0)
      uri
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    packs-specification (0.0.10)
      sorbet-runtime
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-cluster-client (0.13.4)
      redis-client (~> 0.24)
    redis-clustering (5.4.0)
      redis (= 5.4.0)
      redis-cluster-client (>= 0.10.0)
    redis-scripting (1.0.1)
      redis (>= 3.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    sorbet-runtime (0.5.12216)
    stringio (3.1.7)
    thor (1.3.2)
    timecop (0.9.10)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (1.0.3)
    useragent (0.16.11)
    zeitwerk (2.7.2)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  canvas_cache!
  canvas_security!
  config_file!
  debug
  dynamic_settings!
  request_context!
  rspec
  timecop

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
