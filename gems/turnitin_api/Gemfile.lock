PATH
  remote: .
  specs:
    turnitin_api (0.1.0)
      activesupport
      faraday (~> 2.7)
      faraday-follow_redirects (~> 0.3)
      faraday-multipart (~> 1.0)
      inst_statsd
      simple_oauth (~> 0.3)

GEM
  remote: https://rubygems.org/
  specs:
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aroi (1.0.0)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    diff-lcs (1.6.2)
    dogstatsd-ruby (5.7.0)
    drb (2.2.3)
    faraday (2.13.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    hashdiff (1.1.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inst_statsd (3.4.0)
      aroi (>= 0.0.7)
      dogstatsd-ruby (>= 4.2, < 6.0, != 5.0.0)
      statsd-ruby (~> 1.0)
    json (2.13.0)
    logger (1.7.0)
    minitest (5.25.5)
    multipart-post (2.4.1)
    net-http (0.6.0)
      uri
    public_suffix (6.0.2)
    rake (13.2.1)
    rexml (3.4.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    securerandom (0.4.1)
    simple_oauth (0.3.1)
    statsd-ruby (1.5.0)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (1.0.3)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  rake
  rspec (~> 3.12)
  turnitin_api!
  webmock (~> 3.0)

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
