PATH
  remote: .
  specs:
    workflow (0.0.1)
      activesupport (>= 3.2, < 8.0)

GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activerecord (7.2.2.1)
      activemodel (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      timeout (>= 0.4.0)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    drb (2.2.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    logger (1.7.0)
    minitest (5.25.5)
    rake (13.2.1)
    securerandom (0.4.1)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)

PLATFORMS
  aarch64-linux
  arm64-darwin
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  activerecord (>= 3.2, < 8.0)
  rake
  workflow!

RUBY VERSION
   ruby 3.4.1p0

BUNDLED WITH
   2.6.7
