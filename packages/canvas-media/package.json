{"name": "@instructure/canvas-media", "version": "3.0.2", "description": "A component that will handle upload/record and displaying of media", "author": "Instructure, Inc.", "main": "es/index.js", "module": "es/index.js", "sideEffects": false, "scripts": {"i18n:extract": "format-message extract \"es*/**/*.js*\" -g underscored_crc32 -o locales/en.json", "installTranslations": "scripts/installTranslations.js", "commitTranslations": "scripts/commitTranslations.sh", "test": "vitest run", "test:watch": "vitest", "test:debug": "vitest --inspect-brk", "build": "yarn installTranslations && yarn run build:es", "build:es": "tsc", "build:canvas": "yarn run build", "build:watch": "yarn run clean:es && tsc --watch", "clean": "rm -rf lib && yarn run clean:es && rm -rf coverage", "clean:es": "rm -rf es", "prepublishOnly": "yarn build && yarn test", "publishToNpm": "scripts/publish_to_npm.sh"}, "repository": {"type": "git", "url": "git+https://github.com/instructure/canvas-lms.git"}, "license": "MIT", "bugs": {"url": "https://github.com/instructure/canvas-lms/issues"}, "homepage": "https://github.com/instructure/canvas-lms#readme", "dependencies": {"@instructure/emotion": "10.23.0", "@instructure/k5uploader": "*", "@instructure/media-capture": "^9.0.0", "@instructure/studio-player": "1.1.12", "@instructure/ui-a11y-content": "10.23.0", "@instructure/ui-alerts": "10.23.0", "@instructure/ui-billboard": "10.23.0", "@instructure/ui-buttons": "10.23.0", "@instructure/ui-checkbox": "10.23.0", "@instructure/ui-color-utils": "10.23.0", "@instructure/ui-file-drop": "10.23.0", "@instructure/ui-flex": "10.23.0", "@instructure/ui-heading": "10.23.0", "@instructure/ui-icons": "10.23.0", "@instructure/ui-media-player": "^9.0.0", "@instructure/ui-modal": "10.23.0", "@instructure/ui-pagination": "10.23.0", "@instructure/ui-progress": "10.23.0", "@instructure/ui-react-utils": "10.23.0", "@instructure/ui-select": "10.23.0", "@instructure/ui-spinner": "10.23.0", "@instructure/ui-svg-images": "10.23.0", "@instructure/ui-tabs": "10.23.0", "@instructure/ui-text": "10.23.0", "@instructure/ui-text-input": "10.23.0", "@instructure/ui-themes": "10.23.0", "@instructure/ui-toggle-details": "10.23.0", "@instructure/ui-tooltip": "10.23.0", "@instructure/ui-utils": "10.23.0", "@instructure/ui-view": "10.23.0", "@instructure/uid": "10.23.0", "@sheerun/mutationobserver-shim": "^0.3.2", "aphrodite": "^2", "axios": "^0.28.0", "format-message": "^6", "format-message-generate-id": "^6", "lodash": "^4.17.21", "prop-types": "^15", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^12", "@testing-library/user-event": "^14", "@types/react": "*", "@types/react-dom": "*", "jsdom": "*", "shelljs": "^0.8.4", "vitest": "*"}}