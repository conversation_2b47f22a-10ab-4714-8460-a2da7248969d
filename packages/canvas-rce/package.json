{"name": "@instructure/canvas-rce", "version": "7.1.1", "description": "A component wrapping Canvas's usage of Tinymce", "main": "es/index.js", "types": "es/index.d.ts", "owner": "RCX", "scripts": {"i18n:extract": "format-message extract \"es/**/*.js\" -g underscored_crc32 -o locales/en.json", "integration-test": "nightwatch --env integration", "lint": "eslint --quiet", "lint:fix": "eslint --quiet --fix", "test": "yarn jest", "test:parallel": "yarn test:jest", "test:serial": "yarn test:jest", "test:jest": "jest --color", "test:jest:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:jest:watch": "node node_modules/.bin/jest --watch", "demo": "scripts/demo.sh", "demo:clean": "rm -f github-pages/dist/*", "demo:build": "wp -c ./webpack.demo.config.js", "demo:dev": "yarn demo:clean && mkdir -p ./github-pages/dist && cp ./github-pages/index.html ./github-pages/dist && wp -c ./webpack.dev.config.js", "installTranslations": "scripts/installTranslations.js", "commitTranslations": "scripts/commitTranslations.sh", "build": "scripts/build-canvas", "build:es": "babel --out-dir es src --ignore '**/__tests__,**/__mocks__' --extensions '.ts,.tsx,.js,.jsx'", "build:types": "tsc", "build:canvas": "yarn run build", "build:watch": "yarn clean:es && yarn build:es --watch", "prepublishOnly": "yarn build && yarn test", "fmt:check": "biome check", "fmt:fix": "biome format --write", "clean": "rm -rf lib && yarn clean:es && rm -rf coverage && yarn demo:clean", "clean:es": "rm -rf es", "check:ts": "tsc -p tsconfig.json", "generate-svgs": "yarn build:es && node --experimental-modules scripts/generateSvgs.js", "publishToNpm": "scripts/publish_to_npm.sh"}, "keywords": ["canvas", "rich content editor", "rce", "<PERSON><PERSON><PERSON>", "instructure"], "author": "Instructure, Inc", "license": "AGPL-3.0", "browserslist": ["extends @instructure/browserslist-config-canvas-lms"], "nyc": {"include": ["shared/**/*.js", "src/**/*.js"], "exclude": [], "require": ["@babel/register", "@instructure/canvas-theme"], "sourceMap": false, "instrument": false}, "dependencies": {"@instructure/canvas-theme": "10.23.0", "@instructure/canvas-media": "*", "@instructure/debounce": "10.23.0", "@instructure/emotion": "10.23.0", "@instructure/k5uploader": "*", "@instructure/media-capture": "^9.0.0", "@instructure/theme-registry": "10.23.0", "@instructure/ui-a11y-content": "10.23.0", "@instructure/ui-a11y-utils": "10.23.0", "@instructure/ui-alerts": "10.23.0", "@instructure/ui-avatar": "10.23.0", "@instructure/ui-badge": "10.23.0", "@instructure/ui-billboard": "10.23.0", "@instructure/ui-buttons": "10.23.0", "@instructure/ui-checkbox": "10.23.0", "@instructure/ui-source-code-editor": "10.23.0", "@instructure/ui-color-picker": "10.23.0", "@instructure/ui-color-utils": "10.23.0", "@instructure/ui-file-drop": "10.23.0", "@instructure/ui-flex": "10.23.0", "@instructure/ui-focusable": "10.23.0", "@instructure/ui-form-field": "10.23.0", "@instructure/ui-grid": "10.23.0", "@instructure/ui-heading": "10.23.0", "@instructure/ui-icons": "10.23.0", "@instructure/ui-img": "10.23.0", "@instructure/ui-link": "10.23.0", "@instructure/ui-list": "10.23.0", "@instructure/ui-media-player": "^9.0.0", "@instructure/ui-menu": "10.23.0", "@instructure/ui-modal": "10.23.0", "@instructure/ui-motion": "10.23.0", "@instructure/ui-number-input": "10.23.0", "@instructure/ui-overlays": "10.23.0", "@instructure/ui-pagination": "10.23.0", "@instructure/ui-pill": "10.23.0", "@instructure/ui-popover": "10.23.0", "@instructure/ui-radio-input": "10.23.0", "@instructure/ui-simple-select": "10.23.0", "@instructure/ui-spinner": "10.23.0", "@instructure/ui-svg-images": "10.23.0", "@instructure/ui-table": "10.23.0", "@instructure/ui-tabs": "10.23.0", "@instructure/ui-text-area": "10.23.0", "@instructure/ui-text-input": "10.23.0", "@instructure/ui-text": "10.23.0", "@instructure/ui-themes": "10.23.0", "@instructure/ui-toggle-details": "10.23.0", "@instructure/ui-tooltip": "10.23.0", "@instructure/ui-tray": "10.23.0", "@instructure/ui-tree-browser": "10.23.0", "@instructure/ui-truncate-text": "10.23.0", "@instructure/ui-utils": "10.23.0", "@instructure/ui-view": "10.23.0", "@instructure/uid": "10.23.0", "@sheerun/mutationobserver-shim": "^0.3.2", "@types/tinycolor2": "^1.4.6", "@tinymce/tinymce-react": "~3.8.4", "aphrodite": "^2", "axios": "^0.28.0", "bloody-offset": "0.0.0", "crypto-es": "^2.0.4", "classnames": "^2.2.5", "format-message": "^6", "format-message-generate-id": "^6", "i18n-js": "^3", "isomorphic-fetch": "2.2.1", "js-beautify": "1.14.9", "keycode": "^2", "lodash": "^4", "mathlive": "^0.77.0", "minimatch": "~3.0.4", "moment-timezone": "^0.5.45", "prop-types": "^15", "psl": "^1.1.0", "react": "^18", "react-aria-live": "^2", "react-color": "^2.13.4", "react-dom": "^18", "react-draggable": "^3.3.0", "react-redux": "^5", "react-transition-group": "^1", "redux": "^4", "redux-batch-middleware": "^0.2.0", "redux-thunk": "^3.1.0", "text-field-edit": "^3.2.0", "tinycolor2": "^1.6.0", "tinymce": "^5.9", "uri-js": "^4.2.2", "wcag-element-contrast": "^1.0.1"}, "devDependencies": {"@babel/cli": "^7", "@babel/core": "^7", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/preset-typescript": "^7", "@babel/register": "7", "@biomejs/biome": "*", "@instructure/browserslist-config-canvas-lms": ">=2", "@instructure/translations": ">=1", "@testing-library/dom": "^8", "@testing-library/jest-dom": "^5", "@testing-library/react-hooks": "^5", "@testing-library/react": "^12", "@testing-library/user-event": "^14", "@types/react": "^18", "@types/react-dom": "^18", "@types/testing-library__jest-dom": "^5.0.0", "babel-loader": "^9.1.3", "babel-plugin-dynamic-import-node": "^2.2.0", "babel-plugin-inline-json-import": "^0.2.0", "babel-plugin-minify-constant-folding": "^0.5.0", "babel-plugin-minify-dead-code-elimination": "^0.5.0", "babel-plugin-minify-guarded-expressions": "^0.4.3", "babel-plugin-transform-inline-environment-variables": "^0.4.3", "babel-plugin-typescript-to-proptypes": "^2.1.0", "cross-env": "^5.0.0", "escape-html": "^1", "fetch-mock": "^6", "format-message-cli": "^6", "format-message-parse": "^6", "jest-canvas-mock": "^2", "jest-junit": "^7", "jest-mock-proxy": "3.1.2", "jest": "^28", "mathjax": "^3.2.0", "msw": "^2.7", "nyc": "^13", "proxyquire": "1.7.4", "shelljs": "^0.8.3", "skin-deep": "^1", "source-map-support": "0.5.21", "ts-node": "^10.9.2", "typescript": "*", "url-loader": "^4.1.1", "webpack-merge": "^5", "webpack-nano": "^1", "webpack-plugin-serve": "^1", "webpack": "^5"}, "resolutions": {"format-message-estree-util": "./packages/format-message-estree-util"}}