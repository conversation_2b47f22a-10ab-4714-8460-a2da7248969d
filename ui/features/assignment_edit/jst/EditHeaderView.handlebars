{{#if name}}
  <h1 class="screenreader-only">{{name}}</h1>
{{else}}
  {{!-- EVAL-3711 Remove ICE feature flag --}}
  {{#if instui_nav}}
    <h1 class="screenreader-only">{{renderHeaderTitle}}</h1>
  {{else}}
    <h1 class="screenreader-only">{{#t}}New Assignment{{/t}}</h1>
  {{/if}}
{{/if}}

<div class="header-bar assignment-edit-header" style="align-items: flex-start;">
    {{!-- EVAL-3711 Remove ICE feature flag --}}
    {{#if instui_nav}}
      <div class="header-bar-left">
          <h1 class='assignment-edit-header-title'>{{renderHeaderTitle}}</h1>
          <div class="published-assignment-container" style="margin-top:0.75rem"></div>
      </div>
    {{/if}}
    <div class="header-bar-right assignment-edit-header-right">
      <div class="header-group-left assignment-edit-header-group-left">
        {{!-- EVAL-3711 Remove ICE feature flag --}}
        {{#unless instui_nav}}
          {{#if published}}
            <span id='assignment-draft-state' class='published-status published'>
              <i class="icon-publish icon-Solid"></i>
              {{#t}}Published{{/t}}
            </span>
          {{else}}
            <span id='assignment-draft-state' class='published-status unpublished'>
              <i class="icon-unpublished"></i>
              {{#t}}Not Published{{/t}}
            </span>
          {{/if}}
        {{/unless}}
      </div>
      <div class="header-group-right">
        <div class="admin-links">
          <button class="al-trigger btn">
            <span class="screenreader-only">{{#t}}Manage Assignment{{/t}}</span>
            <i class="icon-more" aria-hidden="true"></i>
          </button>
          <ul class="al-options">
            {{#if showSpeedGraderLink}}
              <li class="speed-grader-link-container">
                <a class="icon-speed-grader" href="/courses/{{courseId}}/gradebook/speed_grader?assignment_id={{id}}">
                  {{#t}}SpeedGrader{{/t}}
                </a>
              </li>
            {{/if}}
            {{#unless is_locked}}
              <li class="assignment-delete-container">
              {{#if canDelete}}
                <a href="#" class="delete_assignment_link">
              {{else}}
                <a href="#" class="delete_assignment_link disabled" aria-disabled=true>
              {{/if}}
                  <i class="icon-trash"></i>
                  {{#t}}Delete{{/t}}
                </a>
              </li>
            {{/unless}}
          </ul>
        </div>
      </div>
    </div>
</div>

{{#if CONDITIONAL_RELEASE_SERVICE_ENABLED}}
    <div class='edit-assignment-header-cr'>
        <div id="edit-assignment-header-cr-tabs" class="ui-tabs-minimal">
            <ul id="edit-assignment-header-cr-tab-nav">
                <li><a href="#edit_assignment_wrapper" id="edit_assignment_link">{{#t}}Details{{/t}}</a></li>
                <li><a href="#mastery-paths-editor" id="conditional_release_link">{{#t}}Mastery Paths{{/t}}</a></li>
            </ul>
            <div id="edit-assignment-header-cr-tab-content">
                <form id="edit_assignment_form" class="form-horizontal bootstrap-form" novalidate></form>
            </div>
        </div>
    </div
{{else}}
    <form id="edit_assignment_form" class="form-horizontal bootstrap-form" novalidate></form>
{{/if}}
