/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import {ModuleItem, Module} from '../../utils/types'

export const defaultModuleItems: ModuleItem[] = [
  {
    _id: '0',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 0,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '1',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 1,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '2',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 2,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '3',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 3,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '4',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 4,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '5',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 5,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '6',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 6,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '7',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 7,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '8',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 8,
    content: null,
    masterCourseRestrictions: null,
  },
  {
    _id: '9',
    id: '',
    title: '',
    url: '',
    indent: 0,
    position: 9,
    content: null,
    masterCourseRestrictions: null,
  },
]

export const defaultModules: Module[] = [
  {
    id: '0',
    _id: '0',
    name: '',
    position: 0,
    published: false,
    prerequisites: [],
    completionRequirements: [],
    requirementCount: 0,
    unlockAt: null,
    moduleItems: [],
    requireSequentialProgress: false,
    hasActiveOverrides: false,
  },
  {
    id: '1',
    _id: '1',
    name: '',
    position: 0,
    published: false,
    prerequisites: [],
    completionRequirements: [],
    requirementCount: 0,
    unlockAt: null,
    moduleItems: [],
    requireSequentialProgress: false,
    hasActiveOverrides: false,
  },
  {
    id: '2',
    _id: '2',
    name: '',
    position: 0,
    published: false,
    prerequisites: [],
    completionRequirements: [],
    requirementCount: 0,
    unlockAt: null,
    moduleItems: [],
    requireSequentialProgress: false,
    hasActiveOverrides: false,
  },
  {
    id: '3',
    _id: '3',
    name: '',
    position: 0,
    published: false,
    prerequisites: [],
    completionRequirements: [],
    requirementCount: 0,
    unlockAt: null,
    moduleItems: [],
    requireSequentialProgress: false,
    hasActiveOverrides: false,
  },
  {
    id: '4',
    _id: '4',
    name: '',
    position: 0,
    published: false,
    prerequisites: [],
    completionRequirements: [],
    requirementCount: 0,
    unlockAt: null,
    moduleItems: [],
    requireSequentialProgress: false,
    hasActiveOverrides: false,
  },
]
