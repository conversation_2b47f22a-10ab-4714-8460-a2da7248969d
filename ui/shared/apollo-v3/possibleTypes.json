{"AssetString": ["Course", "Enrollment", "Group"], "AssignedDates": ["Assignment", "Discussion", "Quiz"], "AssignmentOverrideSet": ["AdhocStudents", "Course", "Group", "Noop", "Section"], "AssignmentsConnectionInterface": ["AssignmentGroup", "Course"], "ContentTagContent": ["LearningOutcome"], "DiscussionsConnectionInterface": ["Course"], "FilesConnectionInterface": ["Course"], "LegacyIDInterface": ["Account", "Account<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>up", "AssessmentRequest", "Assignment", "AssignmentGroup", "AuditEvent", "AuditEventExternalTool", "AuditEventQuiz", "AuditEventUser", "CommentBankItem", "CommunicationChannel", "ContentTag", "Course", "CustomGradeStatus", "Discussion", "DiscussionEntry", "DiscussionEntryDraft", "DiscussionEntryVersion", "EligibilityIssue", "ExternalTool", "ExternalUrl", "File", "Folder", "GradingPeriod", "GradingPeriodGroup", "Group", "GroupMembership", "GroupSet", "InternalSetting", "LearningOutcome", "LearningOutcomeGroup", "LtiAsset", "LtiAssetProcessor", "LtiAssetReport", "MediaTrack", "<PERSON><PERSON><PERSON>", "ModuleExternalTool", "ModuleItem", "ModuleProgression", "Notification", "NotificationPolicy", "OutcomeCalculationMethod", "OutcomeFriendlyDescriptionType", "OutcomeProficiency", "Page", "PostPolicy", "ProficiencyRating", "Progress", "ProvisionalGrade", "Quiz", "<PERSON><PERSON><PERSON>", "RubricAsses<PERSON>ent", "RubricAssociation", "RubricCriterion", "RubricRating", "Section", "StandardGradeStatus", "Submission", "SubmissionComment", "SubmissionDraft", "Term", "UsageRights", "User"], "Lockable": ["Assignment", "Discussion", "<PERSON><PERSON><PERSON>", "Page", "Quiz"], "ModuleItemInterface": ["Assignment", "Discussion", "ExternalTool", "ExternalUrl", "File", "ModuleExternalTool", "Page", "Quiz", "SubHeader"], "Node": ["Account", "Assignment", "AssignmentGroup", "CommentBankItem", "CommunicationChannel", "ContentTag", "Conversation", "Course", "CustomGradeStatus", "Discussion", "DiscussionEntry", "Enrollment", "File", "Folder", "GradingPeriod", "GradingPeriodGroup", "GradingStandard", "Group", "GroupSet", "InternalSetting", "LearningOutcome", "LearningOutcomeGroup", "MediaObject", "MessageableContext", "MessageableUser", "<PERSON><PERSON><PERSON>", "ModuleItem", "ModuleProgression", "Notification", "NotificationPolicy", "OutcomeCalculationMethod", "OutcomeFriendlyDescriptionType", "OutcomeProficiency", "Page", "PostPolicy", "Progress", "Quiz", "<PERSON><PERSON><PERSON>", "Section", "StandardGradeStatus", "Submission", "Term", "UsageRights", "User"], "PagesConnectionInterface": ["Course"], "ProgressContext": ["Assignment", "Course", "File", "GroupSet", "User"], "QuizzesConnectionInterface": ["Course"], "SubmissionInterface": ["Submission", "SubmissionHistory"], "Timestamped": ["Account<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>up", "AssessmentRequest", "Assignment", "AssignmentGroup", "AssignmentOverride", "AuditEvent", "CommentBankItem", "CommunicationChannel", "ContentTag", "Course", "Discussion", "DiscussionEntry", "DiscussionEntryDraft", "DiscussionEntryVersion", "Enrollment", "ExternalTool", "ExternalUrl", "File", "Folder", "GradingPeriod", "GradingPeriodGroup", "Group", "GroupMembership", "InboxSettings", "InternalSetting", "LearningOutcome", "<PERSON><PERSON><PERSON>", "ModuleExternalTool", "ModuleItem", "ModuleProgression", "Notification", "NotificationPolicy", "OutcomeAlignment", "OutcomeFriendlyDescriptionType", "Page", "Progress", "Quiz", "Section", "Submission", "SubmissionComment", "SubmissionHistory", "User"], "TurnitinContext": ["File", "Submission"], "VericiteContext": ["File", "Submission"]}